name: 🛡️ NEUROGLYPH Tripla Barriera di Sicurezza Simbolica

on:
  push:
    branches: [ main, develop, fix/* ]
    paths:
      - 'neuroglyph/**'
      - 'scripts/**'
      - 'data/**'
      - 'tools/**'
      - '.github/workflows/tripla_barriera_sicurezza.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'neuroglyph/**'
      - 'scripts/**'
      - 'data/**'
      - 'tools/**'
  workflow_dispatch:
    inputs:
      validation_level:
        description: 'Livello di validazione'
        required: true
        default: 'completo'
        type: choice
        options:
        - 'completo'
        - 'rapido'
        - 'critico'

env:
  PYTHONPATH: ${{ github.workspace }}
  NEUROGLYPH_VALIDATION_MODE: 'ci_tripla_barriera'

jobs:
  barriera-1-registry-linter:
    name: 🔍 BARRIERA 1 - Registry Linter (Unicità Semantica)
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      registry_hash: ${{ steps.registry_validation.outputs.hash }}
      symbols_count: ${{ steps.registry_validation.outputs.count }}
      uniqueness_score: ${{ steps.registry_validation.outputs.uniqueness }}
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: 🐍 Setup Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🔍 Registry Uniqueness Validation
      id: registry_validation
      run: |
        echo "🔍 BARRIERA 1: Registry Linter - Validazione unicità semantica"
        echo "=============================================================="

        # Verify prerequisites
        if [ ! -f "tools/check_symbol_uniqueness.py" ]; then
          echo "💥 CRITICAL: Registry Linter script not found"
          exit 1
        fi

        if [ ! -f "data/neuroglyph_certified_v2_expanded.json" ]; then
          echo "💥 CRITICAL: Registry data not found"
          exit 1
        fi

        # 1. Check symbol uniqueness
        echo "📋 Checking symbol uniqueness..."
        python3 tools/check_symbol_uniqueness.py > uniqueness_report.txt 2>&1
        uniqueness_exit_code=$?
        
        if [ $uniqueness_exit_code -eq 0 ]; then
          echo "✅ Symbol uniqueness: VERIFIED"
        else
          echo "❌ Symbol uniqueness: FAILED"
          cat uniqueness_report.txt
          exit 1
        fi
        
        # 2. Validate registry structure
        echo "🏗️ Validating registry structure..."
        python3 scripts/verify_registry.py > structure_report.txt
        structure_exit_code=$?
        
        if [ $structure_exit_code -eq 0 ]; then
          echo "✅ Registry structure: VALID"
        else
          echo "❌ Registry structure: INVALID"
          cat structure_report.txt
          exit 1
        fi
        
        # 3. Calculate registry hash
        registry_hash=$(find data/ -name "*.json" -type f -exec sha256sum {} \; | sort | sha256sum | cut -d' ' -f1)
        echo "hash=$registry_hash" >> $GITHUB_OUTPUT
        
        # 4. Count symbols
        symbols_count=$(python3 -c "
        import json, glob
        total = 0
        for file in glob.glob('data/neuroglyph_*.json'):
            try:
                with open(file, 'r') as f:
                    data = json.load(f)
                if 'symbols' in data:
                    total += len(data['symbols'])
                elif 'patterns' in data:
                    total += len(data['patterns'])
            except: pass
        print(total)
        ")
        echo "count=$symbols_count" >> $GITHUB_OUTPUT
        
        # 5. Calculate uniqueness score
        uniqueness_score=$(python3 -c "
        import json
        try:
            with open('uniqueness_report.txt', 'r') as f:
                content = f.read()
            if 'PASSED' in content and 'duplicates: 0' in content:
                print('100')
            else:
                print('0')
        except:
            print('0')
        ")
        echo "uniqueness=$uniqueness_score" >> $GITHUB_OUTPUT
        
        echo "📊 BARRIERA 1 RISULTATI:"
        echo "  Registry Hash: $registry_hash"
        echo "  Symbols Count: $symbols_count"
        echo "  Uniqueness Score: $uniqueness_score%"
        
        if [ "$uniqueness_score" = "100" ]; then
          echo "🎉 BARRIERA 1: COMPLETAMENTE SICURA"
        else
          echo "🚨 BARRIERA 1: COMPROMESSA"
          exit 1
        fi

  barriera-2-tokenizer-freeze:
    name: 🧊 BARRIERA 2 - Tokenizer Freeze (Integrità Irreversibile)
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: barriera-1-registry-linter
    outputs:
      tokenizer_hash: ${{ steps.tokenizer_validation.outputs.hash }}
      freeze_status: ${{ steps.tokenizer_validation.outputs.status }}
      integrity_score: ${{ steps.tokenizer_validation.outputs.integrity }}
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: 🐍 Setup Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install transformers tokenizers
        
    - name: 🧊 Tokenizer Freeze Validation
      id: tokenizer_validation
      run: |
        echo "🧊 BARRIERA 2: Tokenizer Freeze - Validazione integrità irreversibile"
        echo "===================================================================="
        
        # 1. Check if tokenizer freeze validator exists
        if [ ! -f "scripts/tokenizer_freeze_validator.py" ]; then
          echo "⚠️ Tokenizer freeze validator not found - creating minimal validation"
          echo "status=not_implemented" >> $GITHUB_OUTPUT
          echo "hash=none" >> $GITHUB_OUTPUT
          echo "integrity=50" >> $GITHUB_OUTPUT
          exit 0
        fi
        
        # 2. Run tokenizer freeze validation
        echo "🔒 Running tokenizer freeze validation..."
        python3 scripts/tokenizer_freeze_validator.py --action validate > tokenizer_report.txt
        tokenizer_exit_code=$?
        
        if [ $tokenizer_exit_code -eq 0 ]; then
          echo "✅ Tokenizer freeze: VERIFIED"
          echo "status=frozen" >> $GITHUB_OUTPUT
          integrity_score="100"
        else
          echo "❌ Tokenizer freeze: COMPROMISED"
          cat tokenizer_report.txt
          echo "status=compromised" >> $GITHUB_OUTPUT
          integrity_score="0"
        fi
        
        # 3. Calculate tokenizer hash if exists
        if [ -d "neuroglyph_isolated_tokenizer" ]; then
          tokenizer_hash=$(find neuroglyph_isolated_tokenizer/ -name "*.json" -type f -exec sha256sum {} \; | sort | sha256sum | cut -d' ' -f1)
        else
          tokenizer_hash="not_found"
        fi
        echo "hash=$tokenizer_hash" >> $GITHUB_OUTPUT
        echo "integrity=$integrity_score" >> $GITHUB_OUTPUT
        
        echo "📊 BARRIERA 2 RISULTATI:"
        echo "  Tokenizer Hash: $tokenizer_hash"
        echo "  Freeze Status: $(cat <<< ${{ steps.tokenizer_validation.outputs.status }})"
        echo "  Integrity Score: $integrity_score%"
        
        if [ "$integrity_score" = "100" ]; then
          echo "🎉 BARRIERA 2: COMPLETAMENTE SICURA"
        elif [ "$integrity_score" = "50" ]; then
          echo "⚠️ BARRIERA 2: NON IMPLEMENTATA"
        else
          echo "🚨 BARRIERA 2: COMPROMESSA"
          exit 1
        fi

  barriera-3-contrastive-training:
    name: ⚔️ BARRIERA 3 - Contrastive Training (Disambiguazione Attiva)
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [barriera-1-registry-linter, barriera-2-tokenizer-freeze]
    outputs:
      dataset_hash: ${{ steps.contrastive_validation.outputs.hash }}
      contrastive_ratio: ${{ steps.contrastive_validation.outputs.ratio }}
      disambiguation_score: ${{ steps.contrastive_validation.outputs.disambiguation }}
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: 🐍 Setup Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: ⚔️ Contrastive Training Validation
      id: contrastive_validation
      run: |
        echo "⚔️ BARRIERA 3: Contrastive Training - Validazione disambiguazione attiva"
        echo "======================================================================"
        
        # 1. Validate symbolic dataset
        echo "📚 Validating symbolic dataset..."
        python3 scripts/symbolic_dataset_validator.py > dataset_report.txt
        dataset_exit_code=$?
        
        if [ $dataset_exit_code -eq 0 ]; then
          echo "✅ Dataset validation: PASSED"
        else
          echo "❌ Dataset validation: FAILED"
          cat dataset_report.txt
          exit 1
        fi
        
        # 2. Check contrastive examples ratio
        echo "🎯 Analyzing contrastive examples..."
        contrastive_analysis=$(python3 -c "
        import json
        try:
            with open('data/neuroglyph_certified_v2_expanded.json', 'r') as f:
                data = json.load(f)
            
            total_patterns = len(data['patterns'])
            contrastive_count = 0
            
            for pattern in data['patterns']:
                metadata = pattern.get('metadata', {})
                if 'contrastive' in metadata or 'negative' in metadata:
                    contrastive_count += 1
            
            ratio = contrastive_count / total_patterns if total_patterns > 0 else 0
            print(f'{contrastive_count},{total_patterns},{ratio:.3f}')
        except Exception as e:
            print('0,0,0.000')
        ")
        
        IFS=',' read -r contrastive_count total_patterns contrastive_ratio <<< "$contrastive_analysis"
        
        echo "ratio=$contrastive_ratio" >> $GITHUB_OUTPUT
        
        # 3. Calculate disambiguation score
        disambiguation_score=$(python3 -c "
        ratio = float('$contrastive_ratio')
        if ratio >= 0.15:
            print('100')  # Excellent
        elif ratio >= 0.10:
            print('80')   # Good
        elif ratio >= 0.05:
            print('60')   # Acceptable
        else:
            print('20')   # Poor
        ")
        echo "disambiguation=$disambiguation_score" >> $GITHUB_OUTPUT
        
        # 4. Calculate dataset hash
        dataset_hash=$(sha256sum data/neuroglyph_certified_v2_expanded.json | cut -d' ' -f1)
        echo "hash=$dataset_hash" >> $GITHUB_OUTPUT
        
        echo "📊 BARRIERA 3 RISULTATI:"
        echo "  Dataset Hash: $dataset_hash"
        echo "  Contrastive Examples: $contrastive_count/$total_patterns ($contrastive_ratio)"
        echo "  Disambiguation Score: $disambiguation_score%"
        
        if [ "$disambiguation_score" -ge "80" ]; then
          echo "🎉 BARRIERA 3: COMPLETAMENTE SICURA"
        elif [ "$disambiguation_score" -ge "60" ]; then
          echo "⚠️ BARRIERA 3: ACCETTABILE"
        else
          echo "🚨 BARRIERA 3: INSUFFICIENTE"
          exit 1
        fi

  fidelity-callback-validation:
    name: 🧠 BARRIERA 4 - Fidelity Callback (Monitoraggio Real-time)
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [barriera-1-registry-linter, barriera-2-tokenizer-freeze, barriera-3-contrastive-training]
    outputs:
      callback_status: ${{ steps.fidelity_validation.outputs.status }}
      monitoring_score: ${{ steps.fidelity_validation.outputs.monitoring }}
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: 🐍 Setup Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🧠 Fidelity Callback Validation
      id: fidelity_validation
      run: |
        echo "🧠 BARRIERA 4: Fidelity Callback - Validazione monitoraggio real-time"
        echo "===================================================================="
        
        # 1. Check if fidelity callback exists
        if [ -f "scripts/neuroglyph_fidelity_callback.py" ]; then
          echo "✅ Fidelity callback found"
          
          # 2. Test callback functionality
          python3 -c "
          import sys
          sys.path.append('scripts')
          from neuroglyph_fidelity_callback import create_fidelity_callback
          
          # Test with dummy patterns
          patterns = [{'input': 'test', 'output': 'test', 'fidelity': 1.0}]
          callback = create_fidelity_callback(patterns, target_fidelity=0.95)
          print('✅ Fidelity callback creation: SUCCESS')
          "
          callback_exit_code=$?
          
          if [ $callback_exit_code -eq 0 ]; then
            echo "status=implemented" >> $GITHUB_OUTPUT
            echo "monitoring=100" >> $GITHUB_OUTPUT
            echo "🎉 BARRIERA 4: COMPLETAMENTE IMPLEMENTATA"
          else
            echo "status=error" >> $GITHUB_OUTPUT
            echo "monitoring=0" >> $GITHUB_OUTPUT
            echo "🚨 BARRIERA 4: ERRORE IMPLEMENTAZIONE"
            exit 1
          fi
        else
          echo "⚠️ Fidelity callback not found"
          echo "status=not_implemented" >> $GITHUB_OUTPUT
          echo "monitoring=50" >> $GITHUB_OUTPUT
          echo "⚠️ BARRIERA 4: NON IMPLEMENTATA"
        fi

  tripla-barriera-summary:
    name: 🏆 Tripla Barriera - Certificazione Finale
    runs-on: ubuntu-latest
    needs: [barriera-1-registry-linter, barriera-2-tokenizer-freeze, barriera-3-contrastive-training, fidelity-callback-validation]
    if: always()
    
    steps:
    - name: 🏆 Certificazione Finale Tripla Barriera
      run: |
        echo "🏆 NEUROGLYPH TRIPLA BARRIERA DI SICUREZZA SIMBOLICA"
        echo "===================================================="
        echo ""
        
        # Collect results
        registry_uniqueness="${{ needs.barriera-1-registry-linter.outputs.uniqueness_score }}"
        tokenizer_integrity="${{ needs.barriera-2-tokenizer-freeze.outputs.integrity_score }}"
        disambiguation_score="${{ needs.barriera-3-contrastive-training.outputs.disambiguation_score }}"
        monitoring_score="${{ needs.fidelity-callback-validation.outputs.monitoring_score }}"
        
        echo "📊 RISULTATI BARRIERE:"
        echo "🔍 BARRIERA 1 - Registry Linter: $registry_uniqueness%"
        echo "🧊 BARRIERA 2 - Tokenizer Freeze: $tokenizer_integrity%"
        echo "⚔️ BARRIERA 3 - Contrastive Training: $disambiguation_score%"
        echo "🧠 BARRIERA 4 - Fidelity Callback: $monitoring_score%"
        echo ""
        
        # Calculate overall security score
        total_score=$(( (registry_uniqueness + tokenizer_integrity + disambiguation_score + monitoring_score) / 4 ))
        
        echo "🔒 PUNTEGGIO SICUREZZA COMPLESSIVO: $total_score/100"
        echo ""
        
        if [ $total_score -ge 90 ]; then
          echo "🎉 CERTIFICAZIONE: TRIPLA BARRIERA COMPLETAMENTE SICURA"
          echo "✅ Tutti i sistemi di sicurezza simbolica sono operativi"
          echo "🚀 AUTORIZZATO per training e deployment in produzione"
        elif [ $total_score -ge 70 ]; then
          echo "⚠️ CERTIFICAZIONE: TRIPLA BARRIERA PARZIALMENTE SICURA"
          echo "🔧 Alcuni sistemi necessitano miglioramenti"
          echo "🚀 AUTORIZZATO per training con monitoraggio aggiuntivo"
        else
          echo "🚨 CERTIFICAZIONE: TRIPLA BARRIERA COMPROMESSA"
          echo "❌ Sistemi di sicurezza insufficienti"
          echo "🛑 NON AUTORIZZATO per training o deployment"
          exit 1
        fi
        
        echo ""
        echo "📋 AUDIT TRAIL:"
        echo "  Timestamp: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
        echo "  Commit: ${{ github.sha }}"
        echo "  Branch: ${{ github.ref_name }}"
        echo "  Registry Hash: ${{ needs.barriera-1-registry-linter.outputs.registry_hash }}"
        echo "  Tokenizer Hash: ${{ needs.barriera-2-tokenizer-freeze.outputs.tokenizer_hash }}"
        echo "  Dataset Hash: ${{ needs.barriera-3-contrastive-training.outputs.dataset_hash }}"
